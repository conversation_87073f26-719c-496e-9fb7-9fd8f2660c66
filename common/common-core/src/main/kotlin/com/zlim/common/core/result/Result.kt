package com.zlim.common.core.result

import com.fasterxml.jackson.annotation.JsonInclude
import java.time.Instant

/**
 * 统一响应结果
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Result<T>(
    val code: Int,
    val message: String,
    val data: T? = null,
    val timestamp: Long = Instant.now().epochSecond
) {
    companion object {
        // 成功状态码
        const val SUCCESS_CODE = 200
        
        // 业务错误状态码
        const val BUSINESS_ERROR_CODE = 400
        
        // 认证错误状态码
        const val AUTH_ERROR_CODE = 401
        
        // 权限错误状态码
        const val PERMISSION_ERROR_CODE = 403
        
        // 资源不存在状态码
        const val NOT_FOUND_CODE = 404
        
        // 服务器错误状态码
        const val SERVER_ERROR_CODE = 500

        /**
         * 成功响应
         */
        fun <T> success(data: T? = null, message: String = "操作成功"): Result<T> {
            return Result(SUCCESS_CODE, message, data)
        }

        /**
         * 失败响应
         */
        fun <T> error(code: Int = BUSINESS_ERROR_CODE, message: String = "操作失败"): Result<T> {
            return Result(code, message)
        }

        /**
         * 业务错误
         */
        fun <T> businessError(message: String): Result<T> {
            return Result(BUSINESS_ERROR_CODE, message)
        }

        /**
         * 认证错误
         */
        fun <T> authError(message: String = "认证失败"): Result<T> {
            return Result(AUTH_ERROR_CODE, message)
        }

        /**
         * 权限错误
         */
        fun <T> permissionError(message: String = "权限不足"): Result<T> {
            return Result(PERMISSION_ERROR_CODE, message)
        }

        /**
         * 资源不存在
         */
        fun <T> notFound(message: String = "资源不存在"): Result<T> {
            return Result(NOT_FOUND_CODE, message)
        }

        /**
         * 服务器错误
         */
        fun <T> serverError(message: String = "服务器内部错误"): Result<T> {
            return Result(SERVER_ERROR_CODE, message)
        }
    }

    /**
     * 判断是否成功
     */
    fun isSuccess(): Boolean = code == SUCCESS_CODE

    /**
     * 判断是否失败
     */
    fun isError(): Boolean = !isSuccess()
}
