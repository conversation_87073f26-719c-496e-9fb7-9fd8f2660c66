package com.zlim.common.core.util

import com.zlim.common.core.enums.BaseErrorCode
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.stereotype.Component
import java.util.*

/**
 * Protobuf国际化工具类
 */
@Component
class ProtoI18nUtils {
    
    companion object {
        private lateinit var instance: ProtoI18nUtils
        
        /**
         * 创建国际化响应构建器
         */
        fun <T> createI18nResponseBuilder(
            builderFactory: () -> T,
            success: Boolean,
            errorCode: BaseErrorCode? = null,
            messageKey: String? = null,
            args: Array<Any>? = null
        ): T where T : Any {
            return instance.buildI18nResponse(builderFactory, success, errorCode, messageKey, args)
        }
        
        /**
         * 获取当前用户语言
         */
        fun getCurrentLocale(): Locale {
            return LocaleContextHolder.getLocale()
        }
        
        /**
         * 获取Accept-Language头中的语言
         */
        fun getAcceptLanguage(): String {
            val locale = getCurrentLocale()
            return when (locale.language) {
                "zh" -> if (locale.country == "TW" || locale.country == "HK") "zh-TW" else "zh-CN"
                "en" -> "en-US"
                else -> "en-US"
            }
        }
    }
    
    init {
        instance = this
    }
    
    /**
     * 构建国际化响应
     */
    fun <T> buildI18nResponse(
        builderFactory: () -> T,
        success: Boolean,
        errorCode: BaseErrorCode? = null,
        messageKey: String? = null,
        args: Array<Any>? = null
    ): T where T : Any {
        val builder = builderFactory()
        
        // 获取消息和消息键
        val (message, key) = when {
            errorCode != null -> {
                val msg = MessageUtils.getMessage(errorCode, args)
                Pair(msg, errorCode.messageKey)
            }
            messageKey != null -> {
                val msg = MessageUtils.getMessage(messageKey, args)
                Pair(msg, messageKey)
            }
            else -> {
                val defaultKey = if (success) "common.success" else "common.operation.failed"
                val msg = MessageUtils.getMessage(defaultKey)
                Pair(msg, defaultKey)
            }
        }
        
        // 使用反射设置字段值
        try {
            // 设置success字段
            val successMethod = builder.javaClass.getMethod("setSuccess", Boolean::class.java)
            successMethod.invoke(builder, success)
            
            // 设置message字段
            val messageMethod = builder.javaClass.getMethod("setMessage", String::class.java)
            messageMethod.invoke(builder, message)
            
            // 设置message_key字段（如果存在）
            try {
                val messageKeyMethod = builder.javaClass.getMethod("setMessageKey", String::class.java)
                messageKeyMethod.invoke(builder, key)
            } catch (e: NoSuchMethodException) {
                // 如果没有messageKey字段，忽略
            }
            
        } catch (e: Exception) {
            // 如果反射失败，记录日志但不抛出异常
            println("Warning: Failed to set i18n fields on protobuf builder: ${e.message}")
        }
        
        return builder
    }
}

/**
 * Protobuf响应构建器扩展
 */
object ProtoResponseBuilder {
    
    /**
     * 创建成功响应
     */
    inline fun <reified T> success(
        builderFactory: () -> T,
        messageKey: String = "common.success",
        args: Array<Any>? = null
    ): T where T : Any {
        return ProtoI18nUtils.createI18nResponseBuilder(
            builderFactory = builderFactory,
            success = true,
            messageKey = messageKey,
            args = args
        )
    }
    
    /**
     * 创建错误响应
     */
    inline fun <reified T> error(
        builderFactory: () -> T,
        errorCode: BaseErrorCode,
        args: Array<Any>? = null
    ): T where T : Any {
        return ProtoI18nUtils.createI18nResponseBuilder(
            builderFactory = builderFactory,
            success = false,
            errorCode = errorCode,
            args = args
        )
    }
    
    /**
     * 创建自定义响应
     */
    inline fun <reified T> custom(
        builderFactory: () -> T,
        success: Boolean,
        messageKey: String,
        args: Array<Any>? = null
    ): T where T : Any {
        return ProtoI18nUtils.createI18nResponseBuilder(
            builderFactory = builderFactory,
            success = success,
            messageKey = messageKey,
            args = args
        )
    }
}

/**
 * Protobuf Builder扩展函数
 */

/**
 * 为任何Protobuf Builder添加国际化支持
 */
fun <T> T.withI18n(
    success: Boolean,
    errorCode: BaseErrorCode? = null,
    messageKey: String? = null,
    args: Array<Any>? = null
): T where T : Any {
    return ProtoI18nUtils.createI18nResponseBuilder(
        builderFactory = { this },
        success = success,
        errorCode = errorCode,
        messageKey = messageKey,
        args = args
    )
}

/**
 * 为Protobuf Builder添加成功消息
 */
fun <T> T.withSuccess(
    messageKey: String = "common.success",
    args: Array<Any>? = null
): T where T : Any {
    return this.withI18n(
        success = true,
        messageKey = messageKey,
        args = args
    )
}

/**
 * 为Protobuf Builder添加错误消息
 */
fun <T> T.withError(
    errorCode: BaseErrorCode,
    args: Array<Any>? = null
): T where T : Any {
    return this.withI18n(
        success = false,
        errorCode = errorCode,
        args = args
    )
}
