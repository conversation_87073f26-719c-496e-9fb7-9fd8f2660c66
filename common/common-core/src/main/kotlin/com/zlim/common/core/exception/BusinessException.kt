package com.zlim.common.core.exception

/**
 * 业务异常
 */
class BusinessException(
    val code: Int = 400,
    override val message: String,
    override val cause: Throwable? = null
) : RuntimeException(message, cause) {

    constructor(message: String) : this(400, message)
    
    constructor(message: String, cause: Throwable) : this(400, message, cause)
}

/**
 * 认证异常
 */
class AuthException(
    override val message: String = "认证失败",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

/**
 * 权限异常
 */
class PermissionException(
    override val message: String = "权限不足",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

/**
 * 资源不存在异常
 */
class NotFoundException(
    override val message: String = "资源不存在",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

/**
 * 参数验证异常
 */
class ValidationException(
    override val message: String = "参数验证失败",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)
