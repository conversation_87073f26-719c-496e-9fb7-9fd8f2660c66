dependencies {
    api(project(":common:common-proto"))
    
    // Spring Boot核心依赖
    api("org.springframework.boot:spring-boot-starter-web") {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-tomcat")
    }
    api("org.springframework.boot:spring-boot-starter-undertow")
    api("org.springframework.boot:spring-boot-starter-data-jpa")
    api("org.springframework.boot:spring-boot-starter-data-redis")
    
    // MyBatis-Plus
    api("com.baomidou:mybatis-plus-boot-starter:${property("mybatisPlusVersion")}")
    api("com.baomidou:mybatis-plus-generator:${property("mybatisPlusVersion")}")
    
    // 数据库驱动
    api("org.postgresql:postgresql")
    api("com.zaxxer:HikariCP")
    
    // Redis客户端
    api("org.redisson:redisson-spring-boot-starter:${property("redissonVersion")}")
    
    // RocketMQ
    api("org.apache.rocketmq:rocketmq-spring-boot-starter:${property("rocketmqVersion")}")
    
    // JWT
    api("io.jsonwebtoken:jjwt-api:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-impl:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-jackson:${property("jjwtVersion")}")
    
    // 工具类
    api("org.apache.commons:commons-lang3")
    api("org.apache.commons:commons-collections4:4.4")
    api("cn.hutool:hutool-all:5.8.29")
    
    // 验证
    api("org.springframework.boot:spring-boot-starter-validation")
    
    // JSON处理
    api("com.fasterxml.jackson.core:jackson-databind")
    api("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    api("com.fasterxml.jackson.module:jackson-module-kotlin")
}
