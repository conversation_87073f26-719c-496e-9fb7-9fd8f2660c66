syntax = "proto3";

package com.zlim.user;

option java_package = "com.zlim.user.proto";
option java_outer_classname = "UserServiceProto";
option java_multiple_files = true;

import "common.proto";

// 用户服务gRPC接口
service UserService {
  // 用户注册
  rpc Register(RegisterRequest) returns (RegisterResponse);
  
  // 用户登录
  rpc Login(LoginRequest) returns (LoginResponse);
  
  // 刷新Token
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  
  // 获取用户信息
  rpc GetUserInfo(GetUserInfoRequest) returns (GetUserInfoResponse);
  
  // 更新用户信息
  rpc UpdateUserInfo(UpdateUserInfoRequest) returns (UpdateUserInfoResponse);
  
  // 验证Token
  rpc VerifyToken(VerifyTokenRequest) returns (VerifyTokenResponse);

  // 用户登出
  rpc Logout(LogoutRequest) returns (LogoutResponse);

  // 批量获取用户信息
  rpc BatchGetUserInfo(BatchGetUserInfoRequest) returns (BatchGetUserInfoResponse);
}

// 注册请求
message RegisterRequest {
  string username = 1;
  string password = 2;
  string email = 3;
  string phone = 4;
  string nickname = 5;
  string verification_code = 6;
}

// 注册响应
message RegisterResponse {
  bool success = 1;
  string message = 2;
  int64 user_id = 3;
  string message_key = 4; // 国际化消息键
}

// 登录请求
message LoginRequest {
  string identifier = 1; // 用户名、邮箱或手机号
  string password = 2;
  string login_type = 3; // password, sms, oauth
  com.zlim.common.DeviceInfo device = 4;
}

// 登录响应
message LoginResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string refresh_token = 4;
  int64 expires_in = 5;
  com.zlim.common.UserInfo user_info = 6;
  string message_key = 7; // 国际化消息键
}

// 刷新Token请求
message RefreshTokenRequest {
  string refresh_token = 1;
}

// 刷新Token响应
message RefreshTokenResponse {
  bool success = 1;
  string access_token = 2;
  string refresh_token = 3;
  int64 expires_in = 4;
}

// 获取用户信息请求
message GetUserInfoRequest {
  int64 user_id = 1;
}

// 获取用户信息响应
message GetUserInfoResponse {
  com.zlim.common.UserInfo user_info = 1;
}

// 更新用户信息请求
message UpdateUserInfoRequest {
  int64 user_id = 1;
  string nickname = 2;
  string avatar = 3;
  string bio = 4;
  map<string, string> extra = 5;
}

// 更新用户信息响应
message UpdateUserInfoResponse {
  bool success = 1;
  string message = 2;
}

// 验证Token请求
message VerifyTokenRequest {
  string access_token = 1;
}

// 验证Token响应
message VerifyTokenResponse {
  bool valid = 1;
  int64 user_id = 2;
  string username = 3;
  int64 expires_at = 4;
}

// 登出请求
message LogoutRequest {
  int64 user_id = 1;
  string access_token = 2;
}

// 登出响应
message LogoutResponse {
  bool success = 1;
  string message = 2;
  string message_key = 3; // 国际化消息键
}

// 批量获取用户信息请求
message BatchGetUserInfoRequest {
  repeated int64 user_ids = 1;
}

// 批量获取用户信息响应
message BatchGetUserInfoResponse {
  repeated com.zlim.common.UserInfo user_infos = 1;
}
